import { Router, Request, Response } from 'express';
import { z } from 'zod';
import { storage } from '../storage';
import { insertSellpassButtonSchema } from '../../shared/schema';

export const sellpassButtonsRouter = Router();

// Middleware to check if user is admin
const checkAdmin = (req: Request, res: Response, next: Function) => {
  if (req.session && req.session.isAdmin) {
    next();
  } else {
    res.status(401).json({ message: 'Unauthorized' });
  }
};

// Get all Sellpass buttons
sellpassButtonsRouter.get('/', checkAdmin, async (req: Request, res: Response) => {
  try {
    const buttons = await storage.getSellpassButtons();
    res.json(buttons);
  } catch (error) {
    console.error('Error fetching Sellpass buttons:', error);
    res.status(500).json({ message: 'Failed to fetch Sellpass buttons' });
  }
});

// Get a specific Sellpass button
sellpassButtonsRouter.get('/:id', checkAdmin, async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const button = await storage.getSellpassButton(parseInt(id));

    if (!button) {
      return res.status(404).json({ message: 'Sellpass button not found' });
    }

    res.json(button);
  } catch (error) {
    console.error('Error fetching Sellpass button:', error);
    res.status(500).json({ message: 'Failed to fetch Sellpass button' });
  }
});

// Create a new Sellpass button
sellpassButtonsRouter.post('/', checkAdmin, async (req: Request, res: Response) => {
  try {
    const validatedData = insertSellpassButtonSchema.parse({
      ...req.body,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    });

    const button = await storage.createSellpassButton(validatedData);
    res.status(201).json(button);
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({ message: 'Validation error', errors: error.errors });
    }
    console.error('Error creating Sellpass button:', error);
    res.status(500).json({ message: 'Failed to create Sellpass button' });
  }
});

// Update a Sellpass button
sellpassButtonsRouter.put('/:id', checkAdmin, async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const updateData = {
      ...req.body,
      updatedAt: new Date().toISOString()
    };

    const button = await storage.updateSellpassButton(parseInt(id), updateData);

    if (!button) {
      return res.status(404).json({ message: 'Sellpass button not found' });
    }

    res.json(button);
  } catch (error) {
    console.error('Error updating Sellpass button:', error);
    res.status(500).json({ message: 'Failed to update Sellpass button' });
  }
});

// Delete a Sellpass button
sellpassButtonsRouter.delete('/:id', checkAdmin, async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const success = await storage.deleteSellpassButton(parseInt(id));

    if (!success) {
      return res.status(404).json({ message: 'Sellpass button not found' });
    }

    res.json({ message: 'Sellpass button deleted successfully' });
  } catch (error) {
    console.error('Error deleting Sellpass button:', error);
    res.status(500).json({ message: 'Failed to delete Sellpass button' });
  }
});
