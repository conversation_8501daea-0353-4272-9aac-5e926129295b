import { storage } from '../storage';

/**
 * Creates a Sellpass button embed for a checkout
 * @param checkoutData The checkout data
 * @param product The product being purchased
 * @returns The Sellpass button embed result
 */
export async function createSellpassButtonEmbed(checkoutData: any, product: any) {
  try {
    // Get the Sellpass button from storage
    const sellpassButton = await storage.getSellpassButton(parseInt(checkoutData.trialSellpassButtonId));
    
    if (!sellpassButton) {
      return {
        id: null,
        url: null,
        buttonHtml: null,
        headScripts: null,
        footerScripts: null,
        error: 'Sellpass button not found',
        isSimulated: true,
        status: 'ERROR'
      };
    }

    // Return the button HTML and scripts
    return {
      id: `sellpass-${Date.now()}`,
      url: null,
      buttonHtml: sellpassButton.buttonHtml,
      headScripts: sellpassButton.headScripts || '',
      footerScripts: sellpassButton.footerScripts || '',
      isSimulated: false,
      status: 'SENT'
    };
  } catch (error) {
    console.error('Error creating Sellpass button embed:', error);
    return {
      id: null,
      url: null,
      buttonHtml: null,
      headScripts: null,
      footerScripts: null,
      error: error instanceof Error ? error.message : 'Unknown error',
      isSimulated: true,
      status: 'ERROR'
    };
  }
}
