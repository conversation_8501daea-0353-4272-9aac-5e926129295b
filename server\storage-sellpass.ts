import { SellpassButton, InsertSellpassButton } from "@shared/schema";

// Add these methods to the MemStorage class in storage.ts
export const sellpassButtonMethods = `
  // Sellpass Button methods
  async getSellpassButtons(): Promise<SellpassButton[]> {
    return Array.from(this.sellpassButtons.values());
  }

  async getSellpassButton(id: number): Promise<SellpassButton | undefined> {
    return this.sellpassButtons.get(id);
  }

  async createSellpassButton(button: InsertSellpassButton): Promise<SellpassButton> {
    const id = this.sellpassButtonCurrentId++;
    const sellpassButton: SellpassButton = { ...button, id };
    this.sellpassButtons.set(id, sellpassButton);
    return sellpassButton;
  }

  async updateSellpassButton(id: number, update: Partial<InsertSellpassButton>): Promise<SellpassButton | undefined> {
    const button = this.sellpassButtons.get(id);
    if (!button) return undefined;

    const updatedButton = { ...button, ...update };
    this.sellpassButtons.set(id, updatedButton);
    return updatedButton;
  }

  async deleteSellpassButton(id: number): Promise<boolean> {
    return this.sellpassButtons.delete(id);
  }

  // Initialize sample Sellpass buttons
  private initializeSellpassButtons() {
    const sampleButtons: InsertSellpassButton[] = [
      {
        name: "Shoppy.gg Button",
        description: "Default Shoppy.gg button for trial checkout",
        headScripts: '<script src="https://shoppy.gg/api/embed.js"></script>',
        buttonHtml: '<button data-shoppy-product="EXAMPLE123" class="shoppy-button">Start Trial</button>',
        footerScripts: '',
        active: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      {
        name: "Sellpass Button",
        description: "Default Sellpass button for trial checkout",
        headScripts: '<script src="https://cdn.sellpass.io/embed/sellpass.js"></script>',
        buttonHtml: '<button data-sellpass-product="EXAMPLE456" class="sellpass-button">Start Trial</button>',
        footerScripts: '',
        active: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    ];

    sampleButtons.forEach(button => {
      this.createSellpassButton(button);
    });
  }
`;
